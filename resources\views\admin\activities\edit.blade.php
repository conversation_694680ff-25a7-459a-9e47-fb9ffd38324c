@extends('layouts.admin')

@section('title', 'แก้ไขกิจกรรม - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-edit me-2 text-warning"></i>แก้ไขกิจกรรม
                    </h1>
                    <p class="text-muted">แก้ไขข้อมูลกิจกรรม: {{ $activity->title }}</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.activities.index') }}">
                                <i class="fas fa-images"></i> จัดการกิจกรรม
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-edit"></i> แก้ไขกิจกรรม
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-10 col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit"></i> แก้ไขกิจกรรม: {{ $activity->title }}
                            </h5>
                        </div>
                        
                        <form action="{{ route('admin.activities.update', $activity) }}" method="POST" enctype="multipart/form-data" id="activityForm">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="remove_cover_image" id="removeCoverImageFlag" value="0">

                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">ชื่อกิจกรรม <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                   id="title" name="title" value="{{ old('title', $activity->title) }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">รายละเอียดกิจกรรม <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="4" required>{{ old('description', $activity->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="category_id" class="form-label">หมวดหมู่ <span class="text-danger">*</span></label>
                                                    <select class="form-select @error('category_id') is-invalid @enderror" id="category_id" name="category_id" required>
                                                        <option value="">เลือกหมวดหมู่</option>
                                                        @foreach(\App\Models\ActivityCategory::active()->get() as $category)
                                                            <option value="{{ $category->id }}" {{ old('category_id', $activity->category_id) == $category->id ? 'selected' : '' }}>
                                                                {{ $category->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('category_id')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="activity_date" class="form-label">วันที่จัดกิจกรรม</label>
                                                    <input type="date" class="form-control @error('activity_date') is-invalid @enderror" 
                                                           id="activity_date" name="activity_date" value="{{ old('activity_date', $activity->activity_date ? $activity->activity_date->format('Y-m-d') : '') }}">
                                                    @error('activity_date')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="location" class="form-label">สถานที่</label>
                                            <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                                   id="location" name="location" value="{{ old('location', $activity->location) }}" placeholder="สถานที่จัดกิจกรรม">
                                            @error('location')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_published" name="is_published" 
                                                       {{ old('is_published', $activity->is_published) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_published">
                                                    เผยแพร่กิจกรรมนี้
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="cover_image" class="form-label">รูปภาพหน้าปก</label>
                                            <input type="file" class="form-control @error('cover_image') is-invalid @enderror"
                                                   id="cover_image" name="cover_image" accept="image/*">
                                            <small class="form-text text-muted">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                                            @error('cover_image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- แสดงชื่อไฟล์ปัจจุบัน -->
                                        <div class="mb-2" id="currentFileName">
                                            @if($activity->cover_image)
                                                <small class="text-primary">
                                                    <i class="fas fa-file-image"></i> ไฟล์ปัจจุบัน: <strong>{{ basename($activity->cover_image) }}</strong>
                                                </small>
                                            @else
                                                <small class="text-muted">
                                                    <i class="fas fa-image"></i> ยังไม่มีรูปภาพ
                                                </small>
                                            @endif
                                        </div>

                                        <!-- รูปภาพปัจจุบัน -->
                                        @if($activity->cover_image)
                                            <div class="text-center" id="imagePreview">
                                                <label class="form-label">รูปภาพปัจจุบัน</label>
                                                <img id="previewImg"
                                                     src="{{ $activity->cover_image_url }}"
                                                     class="img-thumbnail"
                                                     style="max-width: 100%; max-height: 200px;"
                                                     alt="รูปภาพกิจกรรม"
                                                     onerror="this.src='{{ asset('images/no-image.svg') }}'; this.onerror=null;">
                                                <div class="mt-2">
                                                    <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                                        <i class="fas fa-trash"></i> ลบรูปภาพ
                                                    </button>
                                                </div>
                                            </div>
                                        @else
                                            <div class="text-center" id="imagePreview" style="display: none;">
                                                <label class="form-label">รูปภาพใหม่</label>
                                                <img id="previewImg" src="{{ asset('images/no-image.svg') }}"
                                                     class="img-thumbnail" style="max-width: 100%; max-height: 200px;"
                                                     alt="ไม่มีรูปภาพ">
                                                <div class="mt-2">
                                                    <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                                        <i class="fas fa-trash"></i> ลบรูปภาพ
                                                    </button>
                                                </div>
                                            </div>

                                            <div id="noImageDisplay" class="border border-dashed rounded p-4 text-center text-muted">
                                                <i class="fas fa-image fa-3x mb-2"></i>
                                                <p>ไม่มีรูปภาพ</p>
                                            </div>
                                        @endif

                                        <div class="alert alert-info mt-3">
                                            <h6><i class="fas fa-info-circle"></i> สถิติ</h6>
                                            <small>
                                                รูปภาพทั้งหมด: <strong>{{ $activity->images->count() }}</strong> รูป<br>
                                                สร้างเมื่อ: <strong>{{ $activity->created_at->format('d/m/Y H:i') }}</strong>
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gallery Images Section -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-header bg-success text-white">
                                                <h5 class="card-title mb-0">
                                                    <i class="fas fa-images"></i> รูปภาพแกลเลอรี่ ({{ $activity->images->count() }} รูป)
                                                </h5>
                                            </div>
                                            <div class="card-body">
                                                <!-- Upload New Images -->
                                                <div class="mb-4">
                                                    <label for="gallery_images" class="form-label">เพิ่มรูปภาพใหม่</label>
                                                    <input type="file" class="form-control @error('gallery_images.*') is-invalid @enderror"
                                                           id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                                                    <small class="form-text text-muted">
                                                        สามารถเลือกหลายรูปพร้อมกัน | รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB ต่อรูป)
                                                    </small>
                                                    @error('gallery_images.*')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>



                                                <!-- Gallery Images Container -->
                                                <div class="mb-3">
                                                    <h6 class="text-primary"><i class="fas fa-images"></i> รูปภาพในแกลเลอรี่</h6>
                                                    <div id="allImagesContainer">
                                                        <!-- Existing Images -->
                                                        @if($activity->images->count() > 0)
                                                            @foreach($activity->images->sortBy('sort_order') as $image)
                                                                <div class="mb-3" id="image-{{ $image->id }}">
                                                                    <div class="card border-0 shadow-sm">
                                                                        <div class="position-relative">
                                                                            <img src="{{ \App\Helpers\ImageHelper::getImageUrl($image->image_path) }}"
                                                                                 class="card-img-top" style="height: 150px; object-fit: cover;"
                                                                                 alt="รูปภาพกิจกรรม"
                                                                                 onerror="this.src='{{ asset('images/no-image.svg') }}'; this.onerror=null;">
                                                                            <div class="position-absolute top-0 end-0 m-1">
                                                                                <button type="button" class="btn btn-sm btn-danger rounded-circle delete-image"
                                                                                        data-image-id="{{ $image->id }}" data-image-path="{{ $image->image_path }}">
                                                                                    <i class="fas fa-times"></i>
                                                                                </button>
                                                                            </div>
                                                                            <div class="position-absolute bottom-0 start-0 m-1">
                                                                                <span class="badge bg-dark">{{ $loop->iteration }}</span>
                                                                            </div>
                                                                        </div>
                                                                        <div class="card-body p-2">
                                                                            <input type="text" class="form-control form-control-sm image-caption"
                                                                                   placeholder="คำบรรยายรูปภาพ..."
                                                                                   value="{{ $image->caption }}"
                                                                                   data-image-id="{{ $image->id }}">
                                                                            <small class="text-muted">ลำดับ: {{ $image->sort_order + 1 }}</small>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        @endif
                                                    </div>

                                                    @if($activity->images->count() == 0)
                                                        <div class="text-center text-muted py-4" id="noImagesMessage">
                                                            <i class="fas fa-images fa-3x mb-3"></i>
                                                            <p>ยังไม่มีรูปภาพในแกลเลอรี่</p>
                                                            <small>เลือกรูปภาพด้านบนเพื่อเพิ่มลงในแกลเลอรี่</small>
                                                        </div>
                                                    @endif

                                                    <div class="mt-2" id="imageActions" @if($activity->images->count() == 0) style="display: none;" @endif>
                                                        <button type="button" class="btn btn-sm btn-info" id="saveImageCaptions">
                                                            <i class="fas fa-save"></i> บันทึกคำบรรยายรูปภาพ
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-warning" id="reorderImages">
                                                            <i class="fas fa-sort"></i> จัดเรียงรูปภาพ
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary" id="clearNewImages" style="display: none;">
                                                            <i class="fas fa-times"></i> ยกเลิกรูปภาพใหม่ทั้งหมด
                                                        </button>
                                                    </div>
                                                </div>

                                                <!-- Hidden inputs for deleted images -->
                                                <div id="deletedImagesInputs"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card-footer bg-light">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('admin.activities.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> กลับ
                                    </a>
                                    <div>
                                        <a href="{{ route('activities.show', $activity) }}" target="_blank" class="btn btn-info me-2">
                                            <i class="fas fa-eye"></i> ดูหน้าบ้าน
                                        </a>
                                        <a href="#" class="btn btn-success me-2" onclick="manageImages({{ $activity->id }})">
                                            <i class="fas fa-images"></i> จัดการรูปภาพ
                                        </a>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-save"></i> อัปเดตกิจกรรม
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Delete Section -->
                    <div class="card shadow-sm border-0 mt-3">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-trash"></i> ลบกิจกรรม
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">การลบกิจกรรมนี้จะไม่สามารถกู้คืนได้ รูปภาพทั้งหมดจะถูกลบด้วย</p>
                            <button type="button" class="btn btn-danger" id="deleteActivityBtn">
                                <i class="fas fa-trash"></i> ลบกิจกรรมนี้
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@push('styles')
<style>
.card {
    transition: transform 0.2s ease-in-out;
}
.form-control:focus, .form-select:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255,193,7,.25);
}
.border-dashed {
    border-style: dashed !important;
}
.cursor-move {
    cursor: move !important;
}
.image-caption {
    font-size: 0.85rem;
}
.delete-image {
    width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
.position-relative:hover .delete-image {
    opacity: 1;
}
.position-relative .delete-image {
    opacity: 0.7;
    transition: opacity 0.2s;
}
.new-image-preview .card {
    border: 2px solid #198754 !important;
    animation: fadeIn 0.3s ease-in-out;
}
.new-image-preview .badge {
    animation: pulse 1s infinite;
}
@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
.remove-new-image {
    transition: all 0.2s ease-in-out;
    width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
.remove-new-image:hover {
    transform: scale(1.1);
}
#allImagesContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: flex-start;
}
#allImagesContainer > div {
    flex: 0 0 auto;
    margin-bottom: 0 !important;
}
/* Large screens - 4 columns */
@media (min-width: 992px) {
    #allImagesContainer > div {
        flex: 0 0 calc(25% - 0.75rem);
        max-width: calc(25% - 0.75rem);
    }
}
/* Medium screens - 3 columns */
@media (min-width: 768px) and (max-width: 991px) {
    #allImagesContainer > div {
        flex: 0 0 calc(33.333% - 0.667rem);
        max-width: calc(33.333% - 0.667rem);
    }
}
/* Small screens - 2 columns */
@media (min-width: 576px) and (max-width: 767px) {
    #allImagesContainer > div {
        flex: 0 0 calc(50% - 0.5rem);
        max-width: calc(50% - 0.5rem);
    }
}
/* Extra small screens - 1 column */
@media (max-width: 575px) {
    #allImagesContainer > div {
        flex: 0 0 100%;
        max-width: 100%;
    }
    #allImagesContainer {
        gap: 0.5rem;
    }
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
$(document).ready(function() {
    // ตรวจสอบว่ามีรูปจริงๆ หรือไม่
    const hasCoverImage = {{ $activity->cover_image ? 'true' : 'false' }};
    const originalImageUrl = "{{ $activity->cover_image_url ?? '' }}";
    const originalImageName = "{{ $activity->cover_image ? basename($activity->cover_image) : '' }}";

    // แสดงข้อความแจ้งเตือนเมื่ออัปเดทสำเร็จ
    @if(session('success'))
        Swal.fire({
            icon: 'success',
            title: 'สำเร็จ!',
            text: '{{ session('success') }}',
            timer: 3000,
            showConfirmButton: false
        });
    @endif

    // Cover Image preview
    $('#cover_image').on('change', function() {
        let file = this.files[0];
        if (file) {
            // ยกเลิกการลบรูปภาพ (ถ้ามี)
            $('#removeCoverImageFlag').val('0');

            let reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
                $('#noImageDisplay').hide();

                // อัปเดทชื่อไฟล์ที่แสดง
                $('#currentFileName').html(`
                    <small class="text-success">
                        <i class="fas fa-file-image"></i> ไฟล์ใหม่: <strong>${file.name}</strong>
                    </small>
                `);

                // เปลี่ยนปุ่มกลับเป็นลบรูปภาพ
                $('#undoRemove, #removeImage').html('<i class="fas fa-trash"></i> ลบรูปภาพ')
                    .removeClass('btn-warning').addClass('btn-danger').attr('id', 'removeImage');
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove cover image
    $(document).on('click', '#removeImage', function() {
        Swal.fire({
            title: 'ลบรูปภาพหน้าปก?',
            text: 'คุณแน่ใจหรือไม่ที่จะลบรูปภาพหน้าปกนี้?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // ตั้งค่า flag ให้ลบรูปภาพ
                $('#removeCoverImageFlag').val('1');
                $('#cover_image').val('');

                // ซ่อนรูปภาพและแสดงข้อความไม่มีรูป
                $('#imagePreview').hide();
                $('#noImageDisplay').show();

                // ลบข้อความชื่อไฟล์ปัจจุบัน
                $('#currentFileName').html(`
                    <small class="text-danger">
                        <i class="fas fa-trash"></i> รูปภาพจะถูกลบเมื่อกดบันทึก
                    </small>
                `);

                // เปลี่ยนข้อความปุ่ม
                $('#removeImage').html('<i class="fas fa-undo"></i> ยกเลิกการลบ').removeClass('btn-danger').addClass('btn-warning');
                $('#removeImage').attr('id', 'undoRemove');

                // แสดงข้อความแจ้งเตือน
                Swal.fire({
                    title: 'ลบรูปภาพแล้ว!',
                    text: 'รูปภาพจะถูกลบเมื่อคุณกดบันทึก',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    });

    // Undo remove cover image
    $(document).on('click', '#undoRemove', function() {
        Swal.fire({
            title: 'ยกเลิกการลบ?',
            text: 'คุณต้องการยกเลิกการลบรูปภาพหรือไม่?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-undo"></i> ยกเลิกการลบ',
            cancelButtonText: '<i class="fas fa-times"></i> ไม่ต้อง',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // ยกเลิกการลบ
                $('#removeCoverImageFlag').val('0');

                if (hasCoverImage) {
                    $('#imagePreview').show();
                    $('#noImageDisplay').hide();
                    $('#previewImg').attr('src', originalImageUrl);

                    // กลับไปแสดงชื่อไฟล์เดิม
                    if (originalImageName) {
                        $('#currentFileName').html(`
                            <small class="text-primary">
                                <i class="fas fa-file-image"></i> ไฟล์ปัจจุบัน: <strong>${originalImageName}</strong>
                            </small>
                        `);
                    }
                }

                // เปลี่ยนปุ่มกลับ
                $('#undoRemove').html('<i class="fas fa-trash"></i> ลบรูปภาพ').removeClass('btn-warning').addClass('btn-danger');
                $('#undoRemove').attr('id', 'removeImage');

                // แสดงข้อความแจ้งเตือน
                Swal.fire({
                    title: 'ยกเลิกแล้ว!',
                    text: 'รูปภาพจะไม่ถูกลบ',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            }
        });
    });

    // Gallery Images Preview
    let deletedImages = [];
    let newImageFiles = [];

    $('#gallery_images').on('change', function() {
        let files = this.files;
        newImageFiles = Array.from(files); // Store files for form submission

        // Clear previous new images
        $('.new-image-preview').remove();

        if (files.length > 0) {
            // Hide "no images" message if visible
            $('#noImagesMessage').hide();
            $('#imageActions').show();
            $('#clearNewImages').show();

            for (let i = 0; i < files.length; i++) {
                let file = files[i];
                let reader = new FileReader();

                reader.onload = function(e) {
                    let imageCard = `
                        <div class="mb-3 new-image-preview" data-file-index="${i}">
                            <div class="card border-success shadow-sm">
                                <div class="position-relative">
                                    <img src="${e.target.result}" class="card-img-top" style="height: 150px; object-fit: cover;" alt="รูปภาพใหม่">
                                    <div class="position-absolute top-0 end-0 m-1">
                                        <button type="button" class="btn btn-sm btn-danger rounded-circle remove-new-image" data-file-index="${i}">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <div class="position-absolute bottom-0 start-0 m-1">
                                        <span class="badge bg-success">ใหม่</span>
                                    </div>
                                </div>
                                <div class="card-body p-2">
                                    <input type="text" name="captions[]" class="form-control form-control-sm" placeholder="คำบรรยายรูปภาพ (ไม่บังคับ)">
                                    <small class="text-success">ไฟล์: ${file.name}</small>
                                </div>
                            </div>
                        </div>
                    `;
                    $('#allImagesContainer').append(imageCard);
                };

                reader.readAsDataURL(file);
            }
        } else {
            $('#clearNewImages').hide();
            // Show "no images" message if no existing images
            if ($('#allImagesContainer .card').not('.new-image-preview').length === 0) {
                $('#noImagesMessage').show();
                $('#imageActions').hide();
            }
        }
    });

    // Remove individual new image
    $(document).on('click', '.remove-new-image', function() {
        let fileIndex = $(this).data('file-index');
        let removedElement = $(this).closest('.new-image-preview');

        // Remove from newImageFiles array
        newImageFiles.splice(fileIndex, 1);

        // Update file input
        let dt = new DataTransfer();
        newImageFiles.forEach(file => dt.items.add(file));
        document.getElementById('gallery_images').files = dt.files;

        // Remove the element with animation
        removedElement.fadeOut(300, function() {
            $(this).remove();

            // Update file indices for remaining new images
            $('.new-image-preview').each(function(index) {
                $(this).attr('data-file-index', index);
                $(this).find('.remove-new-image').attr('data-file-index', index);
            });

            // Hide clear button if no new images
            if ($('.new-image-preview').length === 0) {
                $('#clearNewImages').hide();
                // Show "no images" message if no existing images
                if ($('#allImagesContainer .card').not('.new-image-preview').length === 0) {
                    $('#noImagesMessage').show();
                    $('#imageActions').hide();
                }
            }
        });
    });

    // Clear New Images
    $('#clearNewImages').on('click', function() {
        Swal.fire({
            title: 'ยกเลิกรูปภาพใหม่?',
            text: 'คุณต้องการยกเลิกรูปภาพใหม่ทั้งหมดหรือไม่?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            cancelButtonText: '<i class="fas fa-arrow-left"></i> กลับ'
        }).then((result) => {
            if (result.isConfirmed) {
                $('#gallery_images').val('');
                $('.new-image-preview').fadeOut(300, function() {
                    $(this).remove();
                });
                newImageFiles = [];
                $('#clearNewImages').hide();

                // Show "no images" message if no existing images
                setTimeout(() => {
                    if ($('#allImagesContainer .card').not('.new-image-preview').length === 0) {
                        $('#noImagesMessage').show();
                        $('#imageActions').hide();
                    }
                }, 300);
            }
        });
    });

    // Delete Existing Image
    $('.delete-image').on('click', function() {
        let imageId = $(this).data('image-id');
        let imagePath = $(this).data('image-path');

        Swal.fire({
            title: 'ลบรูปภาพ?',
            text: 'คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // Add to deleted images array
                deletedImages.push(imageId);

                // Add hidden input for deleted image
                $('#deletedImagesInputs').append(`
                    <input type="hidden" name="deleted_images[]" value="${imageId}">
                `);

                // Remove from DOM
                $(`#image-${imageId}`).fadeOut(300, function() {
                    $(this).remove();

                    // Check if there are no more images
                    if ($('#existingImagesContainer').children().length === 0) {
                        $('#existingImagesContainer').html(`
                            <div class="text-center text-muted py-4 w-100">
                                <i class="fas fa-images fa-3x mb-3"></i>
                                <p>ไม่มีรูปภาพเหลืออยู่</p>
                            </div>
                        `);
                    }
                });

                Swal.fire({
                    title: 'ลบรูปภาพแล้ว!',
                    text: 'รูปภาพจะถูกลบเมื่อคุณกดบันทึก',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            }
        });
    });

    // Save Image Captions
    $('#saveImageCaptions').on('click', function() {
        let captions = [];

        $('.image-caption').each(function() {
            captions.push({
                id: $(this).data('image-id'),
                caption: $(this).val()
            });
        });

        // Send AJAX request to save captions
        $.ajax({
            url: '{{ route("admin.activities.update-captions", $activity->id) }}',
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content'),
                captions: captions
            },
            success: function(response) {
                Swal.fire({
                    title: 'บันทึกสำเร็จ!',
                    text: 'บันทึกคำบรรยายรูปภาพเรียบร้อยแล้ว',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            },
            error: function() {
                Swal.fire({
                    title: 'เกิดข้อผิดพลาด!',
                    text: 'ไม่สามารถบันทึกคำบรรยายรูปภาพได้',
                    icon: 'error',
                    confirmButtonText: 'ตกลง'
                });
            }
        });
    });

    // Reorder Images
    let sortable = null;

    $('#reorderImages').on('click', function() {
        if (sortable) {
            // Disable sorting
            sortable.destroy();
            sortable = null;
            $(this).html('<i class="fas fa-sort"></i> จัดเรียงรูปภาพ');
            $(this).removeClass('btn-success').addClass('btn-warning');

            // Save new order
            let newOrder = [];
            $('#existingImagesContainer .card').each(function(index) {
                let imageId = $(this).find('.image-caption').data('image-id');
                newOrder.push({
                    id: imageId,
                    sort_order: index
                });
            });

            // Send AJAX request to save order
            $.ajax({
                url: '{{ route("admin.activities.update-order", $activity->id) }}',
                type: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    order: newOrder
                },
                success: function(response) {
                    Swal.fire({
                        title: 'บันทึกสำเร็จ!',
                        text: 'บันทึกลำดับรูปภาพเรียบร้อยแล้ว',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false
                    });
                },
                error: function() {
                    Swal.fire({
                        title: 'เกิดข้อผิดพลาด!',
                        text: 'ไม่สามารถบันทึกลำดับรูปภาพได้',
                        icon: 'error',
                        confirmButtonText: 'ตกลง'
                    });
                }
            });
        } else {
            // Enable sorting
            sortable = new Sortable(document.getElementById('existingImagesContainer'), {
                animation: 150,
                ghostClass: 'bg-light',
                handle: '.card',
                onStart: function() {
                    $('.card').addClass('cursor-move');
                },
                onEnd: function() {
                    $('.card').removeClass('cursor-move');
                }
            });

            $(this).html('<i class="fas fa-save"></i> บันทึกลำดับรูปภาพ');
            $(this).removeClass('btn-warning').addClass('btn-success');

            Swal.fire({
                title: 'โหมดจัดเรียง',
                text: 'คุณสามารถลากและวางรูปภาพเพื่อจัดเรียงลำดับใหม่ได้',
                icon: 'info',
                timer: 2000,
                showConfirmButton: false
            });
        }
    });

    // Form validation
    $('#activityForm').on('submit', function(e) {
        let isValid = true;

        // Clear previous errors
        $('.form-control, .form-select').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        // Validate title
        if (!$('#title').val().trim()) {
            $('#title').addClass('is-invalid');
            $('#title').after('<div class="invalid-feedback">กรุณากรอกชื่อกิจกรรม</div>');
            isValid = false;
        }

        // Validate description
        if (!$('#description').val().trim()) {
            $('#description').addClass('is-invalid');
            $('#description').after('<div class="invalid-feedback">กรุณากรอกรายละเอียดกิจกรรม</div>');
            isValid = false;
        }

        // Validate category
        if (!$('#category_id').val()) {
            $('#category_id').addClass('is-invalid');
            $('#category_id').after('<div class="invalid-feedback">กรุณาเลือกหมวดหมู่</div>');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
        }
    });

    // Auto-resize textarea
    $('#description').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Delete activity
    $('#deleteActivityBtn').on('click', function(e) {
        e.preventDefault();

        Swal.fire({
            title: 'ลบกิจกรรม?',
            html: `คุณแน่ใจหรือไม่ที่จะลบกิจกรรม<br><strong>"{{ $activity->title }}"</strong>?<br><small class="text-danger">รูปภาพทั้งหมดจะถูกลบด้วย</small><br><small class="text-muted">การกระทำนี้ไม่สามารถยกเลิกได้</small>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // แสดง loading
                Swal.fire({
                    title: 'กำลังลบ...',
                    text: 'กำลังลบกิจกรรมและรูปภาพทั้งหมด',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // สร้าง form และ submit
                let form = $('<form>', {
                    'method': 'POST',
                    'action': '{{ route('admin.activities.destroy', $activity) }}'
                });

                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '_token',
                    'value': $('meta[name="csrf-token"]').attr('content')
                }));

                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '_method',
                    'value': 'DELETE'
                }));

                $('body').append(form);
                form.submit();
            }
        });
    });
});

function manageImages(activityId) {
    // Open image management in new window/tab
    window.open(`/admin/activities/${activityId}/images`, '_blank');
}
</script>
@endpush

@endsection
