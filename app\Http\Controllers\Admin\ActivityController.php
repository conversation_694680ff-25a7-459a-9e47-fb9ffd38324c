<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\ActivityCategory;
use App\Models\ActivityImage;
use App\Helpers\ImageHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ActivityController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $activities = Activity::with(['category', 'images'])->latest()->get();
        return view('admin.activities.index', compact('activities'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = ActivityCategory::active()->get();
        return view('admin.activities.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:activity_categories,id',
            'activity_date' => 'nullable|date',
            'location' => 'nullable|string|max:255',
            'cover_image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            'images.*' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            'is_published' => 'boolean',
        ]);

        $data = $request->only(['title', 'description', 'category_id', 'activity_date', 'location']);
        $data['is_published'] = $request->boolean('is_published');

        // Handle cover image upload
        if ($request->hasFile('cover_image')) {
            $data['cover_image'] = ImageHelper::uploadAndResize($request->file('cover_image'), 'activities');
        }

        $activity = Activity::create($data);

        // Handle multiple images upload
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $index => $image) {
                $imagePath = ImageHelper::uploadAndResize($image, 'activities/gallery');
                
                ActivityImage::create([
                    'activity_id' => $activity->id,
                    'image_path' => $imagePath,
                    'sort_order' => $index + 1,
                ]);
            }
        }

        return redirect()->route('admin.activities.index')
            ->with('success', 'เพิ่มกิจกรรมสำเร็จ');
    }

    /**
     * Display the specified resource.
     */
    public function show(Activity $activity)
    {
        $activity->load(['category', 'images']);
        return view('admin.activities.show', compact('activity'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Activity $activity)
    {
        $categories = ActivityCategory::active()->get();
        $activity->load(['category', 'images']);
        return view('admin.activities.edit', compact('activity', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Activity $activity)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:activity_categories,id',
            'activity_date' => 'nullable|date',
            'location' => 'nullable|string|max:255',
            'cover_image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            'images.*' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            'is_published' => 'boolean',
        ]);

        $data = $request->only(['title', 'description', 'category_id', 'activity_date', 'location']);
        $data['is_published'] = $request->boolean('is_published');

        // Handle cover image removal
        if ($request->boolean('remove_cover_image')) {
            // Delete old cover image if exists
            if ($activity->cover_image && Storage::disk('public')->exists('activities/' . $activity->cover_image)) {
                Storage::disk('public')->delete('activities/' . $activity->cover_image);
            }
            $data['cover_image'] = null;
        }
        // Handle cover image upload
        elseif ($request->hasFile('cover_image')) {
            // Delete old cover image if exists
            if ($activity->cover_image && Storage::disk('public')->exists('activities/' . $activity->cover_image)) {
                Storage::disk('public')->delete('activities/' . $activity->cover_image);
            }

            $data['cover_image'] = ImageHelper::uploadAndResize($request->file('cover_image'), 'activities');
        }

        $activity->update($data);

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            $currentMaxOrder = $activity->images()->max('sort_order') ?? -1;

            foreach ($request->file('gallery_images') as $index => $image) {
                if ($image && $image->isValid()) {
                    try {
                        $imageErrors = ImageHelper::validateImage($image);
                        if (empty($imageErrors)) {
                            $imagePath = ImageHelper::uploadAndResize($image, 'activities/gallery');
                            ActivityImage::create([
                                'activity_id' => $activity->id,
                                'image_path' => $imagePath,
                                'caption' => $request->captions[$index] ?? null,
                                'sort_order' => $currentMaxOrder + $index + 1,
                            ]);
                        }
                    } catch (\Exception $e) {
                        // Log error but continue with other images
                        \Log::error('Error uploading gallery image: ' . $e->getMessage());
                    }
                }
            }
        }

        // Handle deleted images
        if ($request->has('deleted_images')) {
            foreach ($request->deleted_images as $imageId) {
                $image = ActivityImage::find($imageId);
                if ($image && $image->activity_id == $activity->id) {
                    // Delete image file
                    if (Storage::disk('public')->exists($image->image_path)) {
                        Storage::disk('public')->delete($image->image_path);
                    }
                    // Delete record
                    $image->delete();
                }
            }
        }

        return redirect()->route('admin.activities.edit', $activity)
            ->with('success', 'อัปเดตกิจกรรมสำเร็จ');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Activity $activity)
    {
        // Delete cover image if exists
        if ($activity->cover_image && Storage::disk('public')->exists('activities/' . $activity->cover_image)) {
            Storage::disk('public')->delete('activities/' . $activity->cover_image);
        }

        // Delete all activity images
        foreach ($activity->images as $image) {
            if (Storage::disk('public')->exists('activities/gallery/' . $image->image_path)) {
                Storage::disk('public')->delete('activities/gallery/' . $image->image_path);
            }
        }

        $activity->delete();

        return redirect()->route('admin.activities.index')
            ->with('success', 'ลบกิจกรรมสำเร็จ');
    }

    /**
     * Toggle publish status
     */
    public function togglePublish(Activity $activity)
    {
        $activity->update(['is_published' => !$activity->is_published]);
        
        $status = $activity->is_published ? 'เผยแพร่' : 'ซ่อน';
        
        return response()->json([
            'success' => true,
            'message' => "เปลี่ยนสถานะเป็น {$status} สำเร็จ",
            'is_published' => $activity->is_published
        ]);
    }

    /**
     * Delete activity image
     */
    public function deleteImage(Activity $activity, ActivityImage $image)
    {
        if (Storage::disk('public')->exists('activities/gallery/' . $image->image_path)) {
            Storage::disk('public')->delete('activities/gallery/' . $image->image_path);
        }

        $image->delete();

        return response()->json([
            'success' => true,
            'message' => 'ลบรูปภาพสำเร็จ'
        ]);
    }

    /**
     * Update image order
     */
    public function updateImageOrder(Request $request, Activity $activity)
    {
        $request->validate([
            'images' => 'required|array',
            'images.*.id' => 'required|exists:activity_images,id',
            'images.*.sort_order' => 'required|integer|min:1'
        ]);

        foreach ($request->images as $imageData) {
            ActivityImage::where('id', $imageData['id'])
                ->where('activity_id', $activity->id)
                ->update(['sort_order' => $imageData['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'เรียงลำดับรูปภาพสำเร็จ'
        ]);
    }

    /**
     * Replace activity image
     */
    public function replaceImage(Request $request, ActivityImage $image)
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
        ]);

        // Delete old image
        if (Storage::disk('public')->exists('activities/gallery/' . $image->image_path)) {
            Storage::disk('public')->delete('activities/gallery/' . $image->image_path);
        }

        // Upload new image
        $newImagePath = ImageHelper::uploadAndResize($request->file('image'), 'activities/gallery');
        $image->update(['image_path' => $newImagePath]);

        return response()->json([
            'success' => true,
            'message' => 'เปลี่ยนรูปภาพสำเร็จ',
            'image_url' => asset('storage/activities/gallery/' . $newImagePath)
        ]);
    }

    /**
     * Update image caption
     */
    public function updateImageCaption(Request $request, ActivityImage $image)
    {
        $request->validate([
            'caption' => 'nullable|string|max:255',
        ]);

        $image->update(['caption' => $request->caption]);

        return response()->json([
            'success' => true,
            'message' => 'อัปเดตคำอธิบายรูปภาพสำเร็จ'
        ]);
    }

    /**
     * Bulk delete activities
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:activities,id'
        ]);

        $activities = Activity::with('images')->whereIn('id', $request->ids)->get();
        
        foreach ($activities as $activity) {
            // Delete cover image if exists
            if ($activity->cover_image && Storage::disk('public')->exists('activities/' . $activity->cover_image)) {
                Storage::disk('public')->delete('activities/' . $activity->cover_image);
            }

            // Delete all activity images
            foreach ($activity->images as $image) {
                if (Storage::disk('public')->exists('activities/gallery/' . $image->image_path)) {
                    Storage::disk('public')->delete('activities/gallery/' . $image->image_path);
                }
            }

            $activity->delete();
        }

        return response()->json([
            'success' => true,
            'message' => 'ลบกิจกรรมที่เลือกสำเร็จ (' . count($request->ids) . ' รายการ)'
        ]);
    }

    /**
     * Update image captions
     */
    public function updateCaptions(Request $request, Activity $activity)
    {
        try {
            $captions = $request->input('captions', []);

            foreach ($captions as $captionData) {
                $image = ActivityImage::where('activity_id', $activity->id)
                                    ->where('id', $captionData['id'])
                                    ->first();

                if ($image) {
                    $image->update(['caption' => $captionData['caption']]);
                }
            }

            return response()->json(['success' => true, 'message' => 'บันทึกคำบรรยายสำเร็จ']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Update image order
     */
    public function updateOrder(Request $request, Activity $activity)
    {
        try {
            $order = $request->input('order', []);

            foreach ($order as $orderData) {
                $image = ActivityImage::where('activity_id', $activity->id)
                                    ->where('id', $orderData['id'])
                                    ->first();

                if ($image) {
                    $image->update(['sort_order' => $orderData['sort_order']]);
                }
            }

            return response()->json(['success' => true, 'message' => 'บันทึกลำดับรูปภาพสำเร็จ']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()], 500);
        }
    }
}
