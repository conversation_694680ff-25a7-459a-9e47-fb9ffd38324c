@extends('layouts.admin')

@section('title', 'จัดการกิจกรรม - Admin Panel')

@section('styles')
<style>
    .activity-card {
        transition: all 0.3s ease;
        user-select: none;
    }
    .activity-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        border: 2px solid #ffc107;
    }
    .activity-card:active {
        transform: translateY(-2px);
    }
    .action-bar {
        background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
    }
    .activity-card .card-body {
        pointer-events: none;
    }
    .activity-card .card-body * {
        pointer-events: none;
    }
    .activity-card .form-check,
    .activity-card .dropdown {
        pointer-events: auto;
    }
    .activity-card .form-check *,
    .activity-card .dropdown * {
        pointer-events: auto;
    }
    .gallery-badge {
        background: rgba(0,0,0,0.7);
        backdrop-filter: blur(5px);
    }
    .status-badge {
        backdrop-filter: blur(5px);
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-images me-2 text-warning"></i>จัดการกิจกรรม
                    </h1>
                    <p class="text-muted">จัดการกิจกรรมและแกลเลอรี่รูปภาพ</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-images"></i> จัดการกิจกรรม
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Action Bar -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <button type="button" class="btn btn-warning me-2" data-bs-toggle="modal" data-bs-target="#createActivityModal">
                                        <i class="fas fa-plus"></i> เพิ่มกิจกรรมใหม่
                                    </button>
                                    <button type="button" class="btn btn-danger me-2" id="bulkDeleteBtn" style="display: none;">
                                        <i class="fas fa-trash"></i> ลบที่เลือก
                                    </button>
                                    <span class="text-muted" id="selectedCount"></span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <select class="form-select me-2" id="categoryFilter" style="width: 200px;">
                                        <option value="">ทุกหมวดหมู่</option>
                                        @foreach(\App\Models\ActivityCategory::all() as $category)
                                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                                        @endforeach
                                    </select>
                                    <div class="input-group" style="width: 300px;">
                                        <input type="text" class="form-control" id="searchInput" placeholder="ค้นหากิจกรรม...">
                                        <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activities Grid -->
            <div class="row" id="activitiesGrid">
                @forelse($activities as $activity)
                    <div class="col-lg-4 col-md-6 col-12 mb-4 activity-item" data-activity-id="{{ $activity->id }}" data-category-id="{{ $activity->category_id }}">
                        <div class="card shadow-sm border-0 h-100 activity-card"
                             ondblclick="window.location.href='{{ route('admin.activities.edit', $activity) }}'"
                             style="cursor: pointer;">
                            <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                                <div class="form-check">
                                    <input class="form-check-input activity-checkbox" type="checkbox" value="{{ $activity->id }}"
                                           onclick="event.stopPropagation();">
                                </div>
                                <div class="d-flex align-items-center">
                                    @if($activity->category)
                                        <span class="badge me-2" style="background-color: {{ $activity->category->color }}">
                                            {{ $activity->category->name }}
                                        </span>
                                    @endif
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                                onclick="event.stopPropagation();">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item" href="{{ route('admin.activities.edit', $activity) }}">
                                                    <i class="fas fa-edit text-warning"></i> แก้ไข
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item manage-images" href="#" data-activity-id="{{ $activity->id }}">
                                                    <i class="fas fa-images text-info"></i> จัดการรูปภาพ
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item view-activity" href="{{ route('activities.show', $activity) }}" target="_blank">
                                                    <i class="fas fa-eye text-success"></i> ดูหน้าบ้าน
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <a class="dropdown-item toggle-publish" href="#" data-activity-id="{{ $activity->id }}" data-published="{{ $activity->is_published }}">
                                                    @if($activity->is_published)
                                                        <i class="fas fa-eye-slash text-secondary"></i> ซ่อน
                                                    @else
                                                        <i class="fas fa-eye text-success"></i> เผยแพร่
                                                    @endif
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item delete-activity" href="#" data-activity-id="{{ $activity->id }}">
                                                    <i class="fas fa-trash text-danger"></i> ลบ
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="position-relative">
                                <img src="{{ $activity->cover_image_url }}"
                                     class="card-img-top"
                                     style="height: 200px; object-fit: cover;"
                                     alt="{{ $activity->title }}"
                                     onerror="this.src='{{ asset('images/no-image.svg') }}'; this.onerror=null;">
                                <div class="position-absolute top-0 start-0 m-2">
                                    @if($activity->is_published)
                                        <span class="badge bg-success status-badge">
                                            <i class="fas fa-eye"></i> เผยแพร่
                                        </span>
                                    @else
                                        <span class="badge bg-secondary status-badge">
                                            <i class="fas fa-eye-slash"></i> ซ่อน
                                        </span>
                                    @endif
                                </div>
                                <div class="position-absolute bottom-0 end-0 m-2">
                                    <span class="badge gallery-badge text-white">
                                        <i class="fas fa-images"></i> {{ $activity->images->count() }} รูป
                                    </span>
                                </div>
                            </div>
                            
                            <div class="card-body">
                                <h5 class="card-title mb-2">{{ $activity->title }}</h5>
                                <p class="card-text text-muted mb-3">{{ Str::limit($activity->description, 80) }}</p>

                                <div class="row text-center mb-3">
                                    <div class="col-6">
                                        <div class="border-end">
                                            <small class="text-muted d-block">
                                                <i class="fas fa-calendar-alt"></i> วันที่
                                            </small>
                                            <strong class="text-primary">{{ $activity->formatted_date ?: 'ไม่ระบุ' }}</strong>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted d-block">
                                            <i class="fas fa-map-marker-alt"></i> สถานที่
                                        </small>
                                        <strong class="text-success">{{ Str::limit($activity->location ?: 'ไม่ระบุ', 12) }}</strong>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i> {{ $activity->created_at->diffForHumans() }}
                                    </small>
                                    <div class="d-flex gap-1">
                                        <span class="badge bg-light text-dark">
                                            <i class="fas fa-images"></i> {{ $activity->images->count() }}
                                        </span>
                                        @if($activity->is_published)
                                            <span class="badge bg-success">
                                                <i class="fas fa-eye"></i> Live
                                            </span>
                                        @else
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-eye-slash"></i> Draft
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12">
                        <div class="card shadow-sm border-0">
                            <div class="card-body text-center py-5">
                                <i class="fas fa-images fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">ยังไม่มีกิจกรรม</h5>
                                <p class="text-muted">เริ่มต้นด้วยการเพิ่มกิจกรรมแรกของคุณ</p>
                                <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#createActivityModal">
                                    <i class="fas fa-plus"></i> เพิ่มกิจกรรมใหม่
                                </button>
                            </div>
                        </div>
                    </div>
                @endforelse
            </div>
        </div>
    </section>
</div>

<!-- Create/Edit Activity Modal -->
<div class="modal fade" id="createActivityModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> <span id="modalTitle">เพิ่มกิจกรรมใหม่</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="activityForm" enctype="multipart/form-data">
                @csrf
                <input type="hidden" id="activityId" name="activity_id">
                <input type="hidden" id="formMethod" name="_method" value="POST">
                
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">ชื่อกิจกรรม <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">รายละเอียดกิจกรรม <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="category_id" class="form-label">หมวดหมู่ <span class="text-danger">*</span></label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">เลือกหมวดหมู่</option>
                                            @foreach(\App\Models\ActivityCategory::active()->get() as $category)
                                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="activity_date" class="form-label">วันที่จัดกิจกรรม</label>
                                        <input type="date" class="form-control" id="activity_date" name="activity_date">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="location" class="form-label">สถานที่</label>
                                <input type="text" class="form-control" id="location" name="location" placeholder="สถานที่จัดกิจกรรม">
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_published" name="is_published" checked>
                                    <label class="form-check-label" for="is_published">
                                        เผยแพร่กิจกรรมนี้
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="cover_image" class="form-label">รูปภาพหน้าปก</label>
                                <input type="file" class="form-control" id="cover_image" name="cover_image" accept="image/*">
                                <small class="form-text text-muted">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                            </div>
                            
                            <div id="imagePreview" class="text-center" style="display: none;">
                                <img id="previewImg" src="" class="img-thumbnail" style="max-width: 100%; max-height: 200px;">
                                <div class="mt-2">
                                    <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                        <i class="fas fa-trash"></i> ลบรูปภาพ
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i> <span id="submitBtnText">บันทึก</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('styles')
<style>
.activity-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}
.activity-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}
.activity-checkbox:checked ~ .card {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255,193,7,.25);
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Search functionality
    $('#searchInput').on('keyup', function() {
        filterActivities();
    });

    // Category filter
    $('#categoryFilter').on('change', function() {
        filterActivities();
    });

    function filterActivities() {
        let searchTerm = $('#searchInput').val().toLowerCase();
        let categoryId = $('#categoryFilter').val();

        $('.activity-item').each(function() {
            let activityTitle = $(this).find('.card-title').text().toLowerCase();
            let activityDesc = $(this).find('.card-text').text().toLowerCase();
            let activityCategoryId = $(this).data('category-id');

            let matchesSearch = activityTitle.includes(searchTerm) || activityDesc.includes(searchTerm);
            let matchesCategory = !categoryId || activityCategoryId == categoryId;

            if (matchesSearch && matchesCategory) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }

    // Checkbox selection
    $('.activity-checkbox').on('change', function() {
        updateBulkActions();
    });

    function updateBulkActions() {
        let checkedBoxes = $('.activity-checkbox:checked');
        if (checkedBoxes.length > 0) {
            $('#bulkDeleteBtn').show();
            $('#selectedCount').text(`เลือกแล้ว ${checkedBoxes.length} รายการ`);
        } else {
            $('#bulkDeleteBtn').hide();
            $('#selectedCount').text('');
        }
    }

    // Edit activity
    $('.edit-activity').on('click', function(e) {
        e.preventDefault();
        let activityId = $(this).data('activity-id');
        loadActivityData(activityId);
    });

    function loadActivityData(activityId) {
        $.get(`{{ url('admin/activities') }}/${activityId}`, function(data) {
            $('#activityId').val(data.id);
            $('#title').val(data.title);
            $('#description').val(data.description);
            $('#category_id').val(data.category_id);
            $('#activity_date').val(data.activity_date);
            $('#location').val(data.location);
            $('#is_published').prop('checked', data.is_published);
            $('#formMethod').val('PUT');
            $('#modalTitle').text('แก้ไขกิจกรรม');
            $('#submitBtnText').text('อัปเดต');

            if (data.cover_image_url) {
                $('#previewImg').attr('src', data.cover_image_url);
                $('#imagePreview').show();
            }

            $('#createActivityModal').modal('show');
        });
    }

    // Toggle publish status
    $('.toggle-publish').on('click', function(e) {
        e.preventDefault();
        let activityId = $(this).data('activity-id');
        let isPublished = $(this).data('published');

        $.ajax({
            url: `{{ url('admin/activities') }}/${activityId}/toggle-publish`,
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content'),
                is_published: !isPublished
            },
            success: function() {
                location.reload();
            }
        });
    });

    // Manage images
    $('.manage-images').on('click', function(e) {
        e.preventDefault();
        let activityId = $(this).data('activity-id');
        window.open(`/admin/activities/${activityId}/images`, '_blank');
    });

    // Reset modal when closed
    $('#createActivityModal').on('hidden.bs.modal', function() {
        $('#activityForm')[0].reset();
        $('#activityId').val('');
        $('#formMethod').val('POST');
        $('#modalTitle').text('เพิ่มกิจกรรมใหม่');
        $('#submitBtnText').text('บันทึก');
        $('#imagePreview').hide();
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();
    });

    // Image preview
    $('#cover_image').on('change', function() {
        let file = this.files[0];
        if (file) {
            let reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove image
    $('#removeImage').on('click', function() {
        $('#cover_image').val('');
        $('#imagePreview').hide();
    });

    // Submit form
    $('#activityForm').on('submit', function(e) {
        e.preventDefault();

        // Clear previous errors
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        let formData = new FormData(this);
        let activityId = $('#activityId').val();
        let method = $('#formMethod').val();
        let url = activityId ? `{{ url('admin/activities') }}/${activityId}` : '{{ route('admin.activities.store') }}';

        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }

        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#createActivityModal').modal('hide');
                location.reload();
            },
            error: function(xhr) {
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    let errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(key) {
                        $(`#${key}`).addClass('is-invalid');
                        $(`#${key}`).after(`<div class="invalid-feedback">${errors[key][0]}</div>`);
                    });
                }
            }
        });
    });

    // Delete activity
    $('.delete-activity').on('click', function(e) {
        e.preventDefault();
        let activityId = $(this).data('activity-id');
        let activityTitle = $(this).closest('.activity-item').find('.card-title').text();

        Swal.fire({
            title: 'ลบกิจกรรม?',
            html: `คุณแน่ใจหรือไม่ที่จะลบกิจกรรม<br><strong>"${activityTitle}"</strong>?<br><small class="text-danger">รูปภาพทั้งหมดจะถูกลบด้วย</small>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // แสดง loading
                Swal.fire({
                    title: 'กำลังลบ...',
                    text: 'กำลังลบกิจกรรมและรูปภาพทั้งหมด',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: `{{ url('admin/activities') }}/${activityId}`,
                    type: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function() {
                        Swal.fire({
                            title: 'ลบสำเร็จ!',
                            text: 'กิจกรรมและรูปภาพทั้งหมดถูกลบเรียบร้อยแล้ว',
                            icon: 'success',
                            timer: 1500,
                            showConfirmButton: false
                        }).then(() => {
                            location.reload();
                        });
                    },
                    error: function(xhr) {
                        console.log('Delete error:', xhr);
                        Swal.fire({
                            title: 'เกิดข้อผิดพลาด!',
                            text: 'ไม่สามารถลบกิจกรรมได้ กรุณาลองใหม่อีกครั้ง',
                            icon: 'error',
                            confirmButtonText: 'ตกลง'
                        });
                    }
                });
            }
        });
    });

    // Bulk delete
    $('#bulkDeleteBtn').on('click', function() {
        let selectedIds = $('.activity-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            Swal.fire({
                title: 'ไม่มีรายการที่เลือก',
                text: 'กรุณาเลือกกิจกรรมที่ต้องการลบก่อน',
                icon: 'warning',
                confirmButtonText: 'ตกลง'
            });
            return;
        }

        Swal.fire({
            title: 'ลบกิจกรรมหลายรายการ?',
            html: `คุณแน่ใจหรือไม่ที่จะลบกิจกรรม <strong>${selectedIds.length} รายการ</strong>?<br><small class="text-danger">รูปภาพทั้งหมดจะถูกลบด้วย</small><br><small class="text-muted">การกระทำนี้ไม่สามารถยกเลิกได้</small>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบทั้งหมด',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // แสดง loading
                Swal.fire({
                    title: 'กำลังลบ...',
                    text: `กำลังลบกิจกรรม ${selectedIds.length} รายการ`,
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: '{{ route('admin.activities.bulk-delete') }}',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content'),
                        ids: selectedIds
                    },
                    success: function() {
                        Swal.fire({
                            title: 'ลบสำเร็จ!',
                            text: `ลบกิจกรรม ${selectedIds.length} รายการเรียบร้อยแล้ว`,
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        }).then(() => {
                            location.reload();
                        });
                    },
                    error: function(xhr) {
                        console.log('Bulk delete error:', xhr);
                        Swal.fire({
                            title: 'เกิดข้อผิดพลาด!',
                            text: 'ไม่สามารถลบกิจกรรมได้ กรุณาลองใหม่อีกครั้ง',
                            icon: 'error',
                            confirmButtonText: 'ตกลง'
                        });
                    }
                });
            }
        });
    });
});
</script>
@endpush

@endsection
